const { verify } = require("azure-ad-jwt");
const { executeDatabricksQuery } = require("./databricks");

const ROLES = {
  ADMIN: "Admin",
  DEVELOPER: "Developer",
  READER: "Reader",
};

const jwtConfig = {
  env: {
    issuer: "https://sts.windows.net/be5cc231-0355-4ece-bc68-9fb5f4c9e31b/",
  },
};

const currentConfig = jwtConfig.env;

const authenticate = async (request, context, allowedRoles = []) => {
  const authorizationHeader = request.headers.get("authorization");
  if (!authorizationHeader) {
    throw new Error("Unauthorized");
  }

  const token = authorizationHeader.split(" ")[1];
  try {
    const tokenResult = await new Promise((resolve, reject) => {
      verify(token, currentConfig.issuer, function (err, result) {
        if (result) {
          resolve(result);
        } else {
          context.log(`Token validation failed: ${err}`);
          reject(err);
        }
      });
    });

    const roles = tokenResult.roles || [];
    if (!roles.some((role) => allowedRoles.includes(role))) {
      throw new Error("Forbidden");
    }

    return tokenResult;
  } catch (error) {
    context.log(`Token validation failed: ${error.message}`);
    throw new Error(`${error.message}`);
  }
};

const validatePropertyAccess = async (
  request,
  context,
  allowedRoles = [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER],
  propertyCodeFields = ["propertyCode"]
) => {
  try {
    const tokenResult = await authenticate(request, context, allowedRoles);
    const userEmail = tokenResult.preferred_username || tokenResult.upn || tokenResult.email;

    if (!userEmail) {
      context.log("User email not found in token");
      return {
        success: false,
        response: {
          status: 401,
          jsonBody: {
            error: "Authentication failed",
            message: "User email not found in token",
          },
        },
      };
    }

    const requestBody = await request.json();
    let propertyCode = null;

    for (const field of propertyCodeFields) {
      if (requestBody[field]) {
        propertyCode = requestBody[field];
        break;
      }
    }

    if (!propertyCode) {
      context.log("Property code not found in request body");
      return {
        success: false,
        response: {
          status: 400,
          jsonBody: {
            error: "Property code is required",
            message: `One of the following fields is required: ${propertyCodeFields.join(", ")}`,
          },
        },
      };
    }

    const validationQuery = `
      SELECT user_email, role, EXPLODE(SPLIT(property_list, ',')) AS property_code
      FROM config_dev.gold.master_report_authentication
      WHERE user_email = '${userEmail.replace(/'/g, "''")}'
    `;

    context.log(`Validating property access for user: ${userEmail}, property: ${propertyCode}`);
    const validationResult = await executeDatabricksQuery(validationQuery, context);

    const hasAccess = validationResult.some((row) => row.property_code && row.property_code.trim() === propertyCode.trim());

    if (!hasAccess) {
      context.log(`Access denied for user ${userEmail} to property ${propertyCode}`);
      return {
        success: false,
        response: {
          status: 403,
          jsonBody: {
            error: "Access denied",
            message: `You do not have access to property code: ${propertyCode}`,
          },
        },
      };
    }

    context.log(`Access granted for user ${userEmail} to property ${propertyCode}`);
    return {
      success: true,
      userEmail: userEmail,
      propertyCode: propertyCode,
      requestBody: requestBody,
    };
  } catch (error) {
    context.log(`Error in validatePropertyAccess: ${error.message}`);
    return {
      success: false,
      response: {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
        },
      },
    };
  }
};

module.exports = { authenticate, validatePropertyAccess, ROLES };
