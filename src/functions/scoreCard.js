const { app } = require("@azure/functions");
const { executeDatabricksQuery } = require("./utils/databricks");
const { authenticate, validatePropertyAccess, ROLES } = require("./utils/auth");
const { query: scoreCardQuery, getImagesQuery, getFilterOptionsQuery, getPropertyStrategyQuery } = require("./ScoreCardQuery");
const axios = require("axios");

app.http("getScoreCard", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "score-card",
  handler: async (request, context) => {
    let query = "";
    try {
      // Validate property access
      const validation = await validatePropertyAccess(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER], ["propertyCode"]);
      if (!validation.success) {
        return validation.response;
      }

      const { startDate = "2025-05-02", endDate = "2025-05-08", propertyCode = "67015" } = validation.requestBody;

      query = scoreCardQuery(startDate, endDate, propertyCode);

      const result = await executeDatabricksQuery(query, context);

      const transformedData = {};
      result.forEach((item) => {
        const metricName = item["Collect recovery ratio"].trim().replace(/\s+/g, "_");
        const value = item.value1;
        transformedData[metricName] = value;
      });

      return {
        jsonBody: {
          sql: query,
          data: transformedData,
        },
      };
    } catch (error) {
      context.log(`Error in getScoreCard: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});

app.http("getScoreCardFilterOptions", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "score-card-filter-options",
  handler: async (request, context) => {
    try {
      const tokenResult = await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);
      const userEmail = tokenResult.preferred_username || tokenResult.upn || tokenResult.email;

      if (!userEmail) {
        throw new Error("User email not found in token");
      }

      const propertyQuery = `
        with cte as (select user_email,role,CASE 
                    WHEN LOWER(role) = 'admin' THEN NULL
                    ELSE trim(property_code)
                END  property_code from (
        select user_email,role,
        EXPLODE(SPLIT(property_list, ',')) AS property_code from config_dev.gold.master_report_authentication )
        )
        ,user_properties AS (
            SELECT 
                user_email,
                role,
                trim(property_code) AS property_code
            FROM cte
            WHERE role != 'admin' and user_email='${userEmail}'
        ),
        admin_users AS (
            SELECT DISTINCT user_email, role
            FROM cte 
            WHERE LOWER(role) = 'admin' and user_email='${userEmail}'
        )
        SELECT p.*
            FROM user_properties up
            JOIN (select * from gold_dev.edlh.dim_property where is_Active=true and propertytype<>'Admin') p
              ON up.property_code = trim(p.bu)
        union  
        SELECT p.*
            FROM admin_users au
            CROSS JOIN (select * from gold_dev.edlh.dim_property where is_Active=true and propertytype<>'Admin') p
      `;

      const dateQuery = "select year,month,day_name,month_name,date From gold_dev.edlh.vw_dim_date";
      const systemQuery = "select a.System,a.PropertyKey property_hmy,a.propertyCode BU,a.PropertyName From gold_dev.edlh.vw_dim_source_system a";

      const [propertyResult, dateResult, systemResult] = await Promise.all([
        executeDatabricksQuery(propertyQuery, context),
        executeDatabricksQuery(dateQuery, context),
        executeDatabricksQuery(systemQuery, context),
      ]);

      return {
        jsonBody: {
          data: {
            properties: propertyResult,
            dates: dateResult,
            systems: systemResult,
          },
        },
      };
    } catch (error) {
      context.log(`Error in getScoreCardFilterOptions: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
        },
      };
    }
  },
});

app.http("getJTurnerScore", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "jturner-score",
  handler: async (request, context) => {
    let query = "";
    try {
      // Validate property access
      const validation = await validatePropertyAccess(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER], ["propertyCode"]);
      if (!validation.success) {
        return validation.response;
      }

      const { propertyCode } = validation.requestBody;

      query = `select client_id,property_name,ora_score,national_ora_score,company_ora_score from silver_dev.jturner.ora_scores where client_id='${propertyCode}'`;

      const result = await executeDatabricksQuery(query, context);

      return {
        jsonBody: {
          sql: query,
          data: result,
        },
      };
    } catch (error) {
      context.log(`Error in getJTurnerScore: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});

app.http("getGoogleReviews", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "google-reviews",
  handler: async (request, context) => {
    let query = "";
    try {
      // Validate property access
      const validation = await validatePropertyAccess(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER], ["propertyCode"]);
      if (!validation.success) {
        return validation.response;
      }

      const { propertyCode } = validation.requestBody;

      query = `with cte as (select bu,google_place_id from gold_dev.edlh.dim_property where is_active=true)
              select b.bu propertyBu,title,placeid,rating,addressLines
              from silver_dev.google_api.vw_google_ratings a
              left join cte b on trim(a.placeid)=trim(b.google_place_id)
              where trim(b.bu)='${propertyCode}'`;

      const result = await executeDatabricksQuery(query, context);

      return {
        jsonBody: {
          sql: query,
          data: result,
        },
      };
    } catch (error) {
      context.log(`Error in getGoogleReviews: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});

app.http("getSubmarketOccupancy", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "submarket-occupancy",
  handler: async (request, context) => {
    let query = "";
    try {
      // await authenticate(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER]);
      const { startDate, endDate, subMarketName } = await request.json();

      if (!startDate) {
        return {
          status: 400,
          jsonBody: {
            error: "Start Date is required",
          },
        };
      }

      if (!endDate) {
        return {
          status: 400,
          jsonBody: {
            error: "End Date is required",
          },
        };
      }

      if (!subMarketName) {
        return {
          status: 400,
          jsonBody: {
            error: "Sub Market Name is required",
          },
        };
      }

      query = `
        with 
        cte1 as(select market_name,property,property_id,unit,unit_count,unit_status,start_date,end_date,
        row_number()over (partition by market_name,property_id,unit order by end_date desc) rnk  
        from gold_dev.edlh.fact_msa_data 
        where lower(market_name) like lower('%${subMarketName}%') and start_date <='${startDate}' and end_Date>='${endDate}' 
        and datediff(getdate(),start_date)<120
        ),
        final_cte as(select market_name,property,property_id,unit_count
        ,count(distinct unit) as cnt
        from cte1 where rnk=1  and  trim(unit_status)='Vacant'  group by all
        ),
        property_level_occ as (select market_name,property_id,property,unit_count,cnt,try_divide((unit_count-cnt)*100,unit_count) property_level_occ from final_cte where unit_count>0)
        select market_name,sum(unit_count) total,sum(cnt) vcnt_count,
        try_divide(((sum(unit_count)-sum(cnt))*100),sum(unit_count)) submarket_occupancy
        from property_level_occ group by all
      `;

      const result = await executeDatabricksQuery(query, context);

      return {
        jsonBody: {
          sql: query,
          data: result,
        },
      };
    } catch (error) {
      context.log(`Error in getSubmarketOccupancy: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});

app.http("getPropertyStrategy", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "property-strategy",
  handler: async (request, context) => {
    let query = "";
    try {
      // Validate property access
      const validation = await validatePropertyAccess(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER], ["propertyCode"]);
      if (!validation.success) {
        return validation.response;
      }

      const { startDate = "2025-05-02", endDate = "2025-05-08", propertyCode = "22514" } = validation.requestBody;

      query = getPropertyStrategyQuery(startDate, endDate, propertyCode);

      const result = await executeDatabricksQuery(query, context);

      return {
        jsonBody: {
          sql: query,
          data: result,
        },
      };
    } catch (error) {
      context.log(`Error in getPropertyStrategy: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});

app.http("getImages", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "images",
  handler: async (request, context) => {
    let query = "";
    try {
      // Validate property access
      const validation = await validatePropertyAccess(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER], ["bu"]);
      if (!validation.success) {
        return validation.response;
      }

      const { bu } = validation.requestBody;

      query = getImagesQuery(bu);

      const result = await executeDatabricksQuery(query, context);

      return {
        jsonBody: {
          sql: query,
          data: result,
        },
      };
    } catch (error) {
      context.log(`Error in getImages: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});

app.http("getImagesBase64", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "images-base64",
  handler: async (request, context) => {
    let query = "";
    try {
      // Validate property access
      const validation = await validatePropertyAccess(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER], ["bu"]);
      if (!validation.success) {
        return validation.response;
      }

      const { bu } = validation.requestBody;

      query = getImagesQuery(bu);
      const result = await executeDatabricksQuery(query, context);

      const MAX_CONCURRENT = 3;
      const MAX_IMAGE_SIZE = 5 * 1024 * 1024; // 5MB limit
      const TIMEOUT = 10000; // 10 second timeout

      const processImageBatch = async (images) => {
        return Promise.all(
          images.map(async (image) => {
            if (!image.image_url) return image;

            try {
              const response = await axios.get(image.image_url, {
                responseType: "arraybuffer",
                timeout: TIMEOUT,
                maxContentLength: MAX_IMAGE_SIZE,
                maxBodyLength: MAX_IMAGE_SIZE,
              });

              if (response.data.byteLength > MAX_IMAGE_SIZE) {
                context.log(`Image too large for BU ${image.BU}: ${response.data.byteLength} bytes`);
                return {
                  ...image,
                  image_base64: null,
                  image_error: "Image too large",
                };
              }

              const buffer = Buffer.from(response.data, "binary");
              const contentType = response.headers["content-type"] || "image/jpeg";
              const base64 = `data:${contentType};base64,${buffer.toString("base64")}`;

              return {
                ...image,
                image_url: image.image_url,
                image_base64: base64,
              };
            } catch (error) {
              context.log(`Error converting image to base64 for BU ${image.BU}: ${error.message}`);
              return {
                ...image,
                image_base64: null,
                image_error: error.message,
              };
            }
          })
        );
      };

      const imagesWithBase64 = [];
      for (let i = 0; i < result.length; i += MAX_CONCURRENT) {
        const batch = result.slice(i, i + MAX_CONCURRENT);
        const processedBatch = await processImageBatch(batch);
        imagesWithBase64.push(...processedBatch);
      }

      return {
        jsonBody: {
          sql: query,
          data: imagesWithBase64,
        },
      };
    } catch (error) {
      context.log(`Error in getImagesBase64: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});

// new Score Card queries
app.http("getScoreCardOccupancy", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "score-card-occupancy",
  handler: async (request, context) => {
    let query = "";
    try {
      // Validate property access
      const validation = await validatePropertyAccess(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER], ["propertyCode"]);
      if (!validation.success) {
        return validation.response;
      }

      const { startDate = "2025-06-08", endDate = "2025-06-15", propertyCode = "22514" } = validation.requestBody;

      query = `
        with 
        dates1 as (select cast('${startDate}' as date) startdate,'${endDate}' as enddate ,'${propertyCode}' property_code ),
        dates as(
        select cast((select enddate from dates1) as date)-30 t_30
        ,cast((select enddate from dates1) as date)-30 t_31
        , startdate
        , enddate  enddate
        ,add_months(cast((select enddate from dates1) as date),-12) as prevyrenddate
        , property_code 
        ,date_Sub(trunc(CAST((select enddate from dates1) AS DATE), 'YYYY') ,31-
        (select distinct cast(icloseday as int) from gold_dev.edlh.fact_financial_perf_ytd where trim(property_Code)=(select property_code from dates1)) ) as adjusted_period_start
        ,case when day(enddate) >= (select distinct cast(icloseday as int) from gold_dev.edlh.fact_financial_perf_ytd where trim(property_Code)=(select property_code from dates1)) 
        then MAKE_DATE(YEAR(enddate), MONTH(enddate), (select distinct cast(icloseday as int) from gold_dev.edlh.fact_financial_perf_ytd where trim(property_Code)=(select property_code from dates1)))  
        else MAKE_DATE(YEAR(enddate), MONTH(enddate)-1,
        case when day(last_day(add_months(CAST((select enddate from dates1) AS DATE), -1)))<  (select distinct cast(icloseday as int) from gold_dev.edlh.fact_financial_perf_ytd where trim(property_Code)=(select property_code from dates1)) 
        then day(last_day(add_months(CAST((select enddate from dates1) AS DATE), -1)))
        else (select distinct cast(icloseday as int) from gold_dev.edlh.fact_financial_perf_ytd where trim(property_Code)=(select property_code from dates1))  end
        )  end as adjusted_period_end
        , cast((select enddate from dates1) as date)-60 t_60
        ,trunc(CAST((select enddate from dates1) AS DATE), 'YYYY') 1st_day_of_year
        ,last_day(add_months(CAST((select enddate from dates1) AS DATE), -1)) AS last_day_prev_month
        ,trunc(CAST((select enddate from dates1) AS DATE), 'MM') AS first_day_of_month  
        from dates1
        ) 
        ,
        cte_occu_t30 as (select property_code,unit_code,sstatus
        ,row_number()over(partition by unit_code order by unit_status_hmy desc)rnk
         from gold_dev.edlh.fact_property_unitscorecard 
         where trim(property_code) in (select property_code from dates)
        and dtstart<=(select t_30 from dates)  and trim(unit_status) !='Excluded') ,
        cte as (select property_code,unit_code,sstatus
        ,row_number()over(partition by unit_code order by unit_status_hmy desc)rnk
         from gold_dev.edlh.fact_property_unitscorecard 
         where trim(property_code) in (select property_code from dates) and trim(unit_status) !='Excluded'
        and dtstart<=(select enddate from dates)  ) ,
        cte_vacant as (select property_code,unit_code,sstatus,dtstart,case when dtvacant is null or trim(dtvacant)='' then dtstart else dtvacant end dtvacant
        ,case when dtend>(select enddate from dates) then (select enddate from dates) else dtend end dtend
        ,row_number()over(partition by unit_code order by unit_status_hmy desc) rnk1
        from gold_dev.edlh.fact_property_unitscorecard where trim(property_code)in(select property_code from dates) and trim(unit_status) !='Excluded'
        and dtstart <=(select enddate from dates)  and (dtend>=(select enddate from dates) or dtend is null )),
        cte_unit_Ava as (
        select property_code  ,unit_code ,sStatus ,
        row_number()over (partition by unit_hmy order by unit_status_hmy desc) rk
        From gold_dev.edlh.fact_property_unitscorecard vspusc where trim(property_code)in (select property_code from dates) 
        and trim(unit_status) !='Excluded'
        and cast(dtStart as date)<=(select enddate from dates) and trim(unit_status) !='Excluded'
        ) 

        --- Vacant units 
        select 'vcnt units'parameter,count(*) vcnt_units
        from cte_vacant where rnk1=1 and sstatus in ('Vacant Unrented Ready', 'Vacant Unrented Not Ready', 'Vacant Rented Not Ready','Vacant Rented Ready')
        union all 
        select 'avg vcnt days'parameter,avg(days_vacant) from(
        select 
          datediff(day,dtvacant,(select enddate from dates)) days_vacant
        from cte_vacant where rnk1=1 and sstatus like 'Vacant%' ) where days_vacant>30
        union all 
        select 'aged vacant units'parameter,count(distinct unit_code)cnt 
         from (select unit_code,datediff(day,dtvacant,(select enddate from dates)) days_vacant
        from cte_vacant where rnk1=1 and sstatus in('Vacant Unrented Ready', 'Vacant Unrented Not Ready', 'Vacant Rented Not Ready','Vacant Rented Ready'))
        where days_vacant>30
        union all 
        select 'Occupancy_Non_Rev' Parameter,try_divide((((select count(*) occ_non_rev from cte where
        sstatus in ('Occupied No Notice','Notice Unrented','Notice Rented')
        and rnk=1) + (select count(distinct unit_code)  from gold_dev.edlh.fact_property_unitscorecard
         where trim(property_code)in(select property_code from dates) and trim(sstatus) in ('Admin','Down','Model')
        and (dtEnd>=(select enddate from dates) or dtend is null)))*100),(select count(distinct unit_code) from  gold_dev.edlh.fact_property_unitscorecard
         where trim(property_code)in(select property_code from dates)and trim(unit_status) !='Excluded')) Occupancy_Non_Rev
         union all 

        ------------
        --Occupancy Trend
        select 'Occupancy_Trend' Parameter,try_divide((((select count(distinct unit_code) from  gold_dev.edlh.fact_property_unitscorecard
         where trim(property_code)in (select property_code from dates)and trim(unit_status) !='Excluded')
         -(select count(*) occ_non_rev from cte where  sstatus in  ('Vacant Unrented Ready','Vacant Unrented Not Ready','Notice Unrented')  and rnk=1))*100)
         ,(select count(distinct unit_code) from  gold_dev.edlh.fact_property_unitscorecard
         where trim(property_code) in (select property_code from dates) and trim(unit_status) !='Excluded')) Occupancy_Trend
        union all 
        ---gain loss
        select 'gain_loss'parameter 
        ,sum(case when sevent='Cancel Notice' then cnt else 0 end )
        -sum(case when sevent in ('Notice Given', 'Early Termination', 'Skip') then cnt else 0 end)
        +sum(case when sevent in ('Submit Application') then cnt else 0 end)
        -sum(case when sevent in ('Cancel Move In') then cnt else 0 end )
        -sum(case when sevent in ('Application Denied') then cnt else 0 end) gainLoss
        --CancelNotices-Notices+SubmitApp-MovinCancel-AppDenail
         from (
        select sevent,count(*) cnt
        from gold_dev.edlh.fact_prospect where cast(event_logged_Date as date)>=(select startdate from dates) and cast(event_logged_Date as date)<=(select enddate from dates)
        and trim(property_scode)in(select property_code from dates)
        group by sevent)
        union all
        ------ available units
        select 'units available' parameter
        ,count(distinct unit_code) from cte_unit_Ava 
        where rk=1 and sstatus in ('Vacant Unrented Ready', 'Vacant Unrented Not Ready', 'Notice Unrented') 
        union all 
        --- T30 
        select 't30 show' parameter, count(*) T30  from gold_dev.edlh.fact_prospect 
        where cast(event_logged_Date as date)>=(select t_31 from dates) and cast(event_logged_Date as date)<=(select enddate from dates)
        and trim(property_scode)in(select property_code from dates) and trim(sevent)='Show' and trim(bfirstshow)='-1'
        union all
        select 'Occupancy_Trendt30' Parameter,try_divide((((select count(distinct unit_code) from  gold_dev.edlh.fact_property_unitscorecard
         where trim(property_code) in (select property_code from dates) and trim(unit_status) !='Excluded')-(select count(*) occ_non_rev from cte_occu_t30
         where  sstatus in  ('Vacant Unrented Ready','Vacant Unrented Not Ready','Notice Unrented')  and rnk=1))*100)
         ,(select count(distinct unit_code) from  gold_dev.edlh.fact_property_unitscorecard  where trim(property_code) in (select property_code from dates)
         and trim(unit_status) !='Excluded')) Occupancy_Trendt30
         union all
        Select 'ColorcodingOfT30' Parameter, (try_divide(try_divide(try_divide((UnitCount),try_divide(UnitCount,2)),12),35))*100 as ColorcodingOfT30
        from gold_dev.edlh.dim_property 
        where trim(BU)=(select property_code from dates)
      `;

      const result = await executeDatabricksQuery(query, context);

      const transformedData = {};
      result.forEach((item) => {
        const parameterName = item.parameter?.trim().replace(/\s+/g, "_");
        let value = null;
        if (item.vcnt_units !== undefined) value = item.vcnt_units;
        else if (item.days_vacant !== undefined) value = item.days_vacant;
        else if (item.cnt !== undefined) value = item.cnt;
        else if (item.Occupancy_Non_Rev !== undefined) value = item.Occupancy_Non_Rev;
        else if (item.gainLoss !== undefined) value = item.gainLoss;
        else if (item.T30 !== undefined) value = item.T30;
        else if (item.Occupancy_Trendt30 !== undefined) value = item.Occupancy_Trendt30;
        else if (item.ColorcodingOfT30 !== undefined) value = item.ColorcodingOfT30;
        else if (item.value1 !== undefined) value = item.value1;

        if (parameterName && value !== null) {
          transformedData[parameterName] = value;
        }
        // If value is 0, still share
        if (parameterName && value === 0) {
          transformedData[parameterName] = 0;
        }
      });

      return {
        jsonBody: {
          sql: query,
          data: transformedData,
        },
      };
    } catch (error) {
      context.log(`Error in getScoreCardOccupancy: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});

app.http("getScoreCardRent", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "score-card-rent",
  handler: async (request, context) => {
    let query = "";
    try {
      // Validate property access
      const validation = await validatePropertyAccess(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER], ["propertyCode"]);
      if (!validation.success) {
        return validation.response;
      }

      const { startDate = "2025-06-08", endDate = "2025-06-15", propertyCode = "22514" } = validation.requestBody;

      query = ` 
      with 
      dates1 as (select cast('${startDate}' as date) startdate,'${endDate}' as enddate ,'${propertyCode}' property_code ),
      dates as(
      select cast((select enddate from dates1) as date)-30 t_30
      ,cast((select enddate from dates1) as date)-30 t_31
      , startdate
      , enddate  enddate
      ,add_months(cast((select enddate from dates1) as date),-12) as prevyrenddate
      , property_code 
      ,date_Sub(trunc(CAST((select enddate from dates1) AS DATE), 'YYYY') ,31-
      (select distinct cast(icloseday as int) from gold_dev.edlh.fact_financial_perf_ytd where trim(property_Code)=(select property_code from dates1)) ) as adjusted_period_start
      ,case when day(enddate) >= (select distinct cast(icloseday as int) from gold_dev.edlh.fact_financial_perf_ytd where trim(property_Code)=(select property_code from dates1)) 
        then MAKE_DATE(YEAR(enddate), MONTH(enddate), (select distinct cast(icloseday as int) from gold_dev.edlh.fact_financial_perf_ytd where trim(property_Code)=(select property_code from dates1)))  
        else MAKE_DATE(YEAR(enddate), MONTH(enddate)-1,
        case when day(last_day(add_months(CAST((select enddate from dates1) AS DATE), -1)))<  (select distinct cast(icloseday as int) from gold_dev.edlh.fact_financial_perf_ytd where trim(property_Code)=(select property_code from dates1)) 
        then day(last_day(add_months(CAST((select enddate from dates1) AS DATE), -1)))
        else (select distinct cast(icloseday as int) from gold_dev.edlh.fact_financial_perf_ytd where trim(property_Code)=(select property_code from dates1))  end
      )  end as adjusted_period_end
      , cast((select enddate from dates1) as date)-60 t_60
      ,trunc(CAST((select enddate from dates1) AS DATE), 'YYYY') 1st_day_of_year
      ,last_day(add_months(CAST((select enddate from dates1) AS DATE), -1)) AS last_day_prev_month
      ,trunc(CAST((select enddate from dates1) AS DATE), 'MM') AS first_day_of_month  
      from dates1
      ),
      cte as (select property_code,unit_code,sstatus
      ,row_number()over(partition by unit_code order by unit_status_hmy desc)rnk
       from gold_dev.edlh.fact_property_unitscorecard 
       where trim(property_code) in (select property_code from dates) and trim(unit_status) !='Excluded'
      and dtstart<=(select enddate from dates)  ) 
      
      --In place rent
      select 'In_Place_rent' parameter,avg(effective_rent) In_Place_rent
       from gold_dev.edlh.fact_lease_history 
      where enddate>=(select enddate from dates) and dtleasefrom<=(select enddate from dates)
      and trim(property_code)in(select property_code from dates) and rnk>=1 ---Note: rnk = Lease no
      union all 
      select ' In_Place_rent/sqft' parameter,try_divide(sum(effective_rent),sum(dsqft)) Renewal_Place_rent
       from gold_dev.edlh.fact_lease_history 
      where enddate>=(select enddate from dates) and dtleasefrom<=(select enddate from dates)
      and trim(property_code)in(select property_code from dates) and rnk>=1
      union all
      --Previous Year In place rent
      select 'Prev_Yr_In_Place_rent' parameter,avg(effective_rent) In_Place_rent
      from gold_dev.edlh.fact_lease_history 
      where  enddate>=add_months(CAST((select enddate from dates) AS DATE), -12) 
      and dtleasefrom<=add_months(CAST((select enddate from dates) AS DATE), -12)
      and trim(property_code)in(select property_code from dates) and rnk>=1
      union all 
      --New In place rent
      select 'New In_Place_rent' parameter,avg(effective_rent) Renewal_Place_rent
       from gold_dev.edlh.fact_lease_history 
      where enddate>=(select enddate from dates) and dtleasefrom<=(select enddate from dates)
      and trim(property_code)in(select property_code from dates) and rnk=1
      union all 
      select 'New In_Place_rent/sqft' parameter,try_divide(sum(effective_rent),sum(dsqft)) Renewal_Place_rent
       from gold_dev.edlh.fact_lease_history 
      where enddate>=(select enddate from dates) and dtleasefrom<=(select enddate from dates)
      and trim(property_code)in(select property_code from dates) and rnk=1
      Union all
      --Previous year New In place rent
      select 'Prev_Yr_New In_Place_rent' parameter,avg(effective_rent) Renewal_Place_rent
       from gold_dev.edlh.fact_lease_history 
      where enddate>=add_months(CAST((select enddate from dates) AS DATE), -12) 
      and dtleasefrom<=add_months(CAST((select enddate from dates) AS DATE), -12)
      and trim(property_code)in(select property_code from dates) and rnk=1
      union all 
      --renewal In place rent
      select 'Renewal In_Place_rent' parameter,avg(effective_rent) Renewal_Place_rent
       from gold_dev.edlh.fact_lease_history 
      where enddate>=(select enddate from dates) and dtleasefrom<=(select enddate from dates)
      and trim(property_code)in(select property_code from dates) and rnk>1
      union all 
      select 'Renewal In_Place_rent/sqft' parameter,try_divide(sum(effective_rent),sum(dsqft)) Renewal_Place_rent
       from gold_dev.edlh.fact_lease_history 
      where enddate>=(select enddate from dates) and dtleasefrom<=(select enddate from dates)
      and trim(property_code)in(select property_code from dates) and rnk>1
      union all
      --previous year renewal In place rent
      select 'Prev_Yr_Renewal In_Place_rent' parameter,avg(effective_rent) Renewal_Place_rent
       from gold_dev.edlh.fact_lease_history 
      where  enddate>=add_months(CAST((select enddate from dates) AS DATE), -12) 
      and dtleasefrom<=add_months(CAST((select enddate from dates) AS DATE), -12)
      and trim(property_code)in(select property_code from dates) and rnk>1
      union all 
      ---MTM
      select 'MTM' parameter,
      (try_divide((select count(distinct unit_code) From   gold_dev.edlh.fact_lease_history
      where enddate>=(select enddate from dates) and dtleasefrom<=(select enddate from dates)
      and trim(property_code)in(select property_code from dates) and dtleaseto<(select enddate from dates)),
      (select count(unit_code) From   gold_dev.edlh.fact_lease_history
      where enddate>=(select enddate from dates) and dtleasefrom<=(select enddate from dates)
      and trim(property_code)in(select property_code from dates))))*100
      union all 
      select 'YTD Renewal Conversion' ,try_divide(((select count(*) from gold_dev.edlh.fact_lease_history where trim(property_code) in  (select property_code from dates)
      and COALESCE(earlytermination,dtleaseto)>=(select adjusted_period_start from dates)
      and COALESCE(earlytermination,dtleaseto)<=(select adjusted_period_end from dates)
      and trim(leaseexpirationresult)='Lease Renewed')*100),(select count(*) from gold_dev.edlh.fact_lease_history
      where trim(property_code) in (select property_code from dates)
      and COALESCE(earlytermination,dtleaseto)>=(select adjusted_period_start from dates)
      and COALESCE(earlytermination,dtleaseto)<=(select adjusted_period_end from dates))) YTD_Renewal_Conv
      `;

      const result = await executeDatabricksQuery(query, context);

      const transformedData = {};
      result.forEach((item) => {
        const parameterName = item.parameter?.trim().replace(/\s+/g, "_");
        let value = null;
        if (item.In_Place_rent !== undefined) value = item.In_Place_rent;
        else if (item.Renewal_Place_rent !== undefined) value = item.Renewal_Place_rent;
        else if (item.YTD_Renewal_Conv !== undefined) value = item.YTD_Renewal_Conv;
        else if (item.value1 !== undefined) value = item.value1;

        if (parameterName) {
          transformedData[parameterName] = value;
        }
      });

      return {
        jsonBody: {
          sql: query,
          data: transformedData,
        },
      };
    } catch (error) {
      context.log(`Error in getScoreCardRent: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});

app.http("getScoreCardFinancial", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "score-card-financial",
  handler: async (request, context) => {
    let query = "";
    try {
      // Validate property access
      const validation = await validatePropertyAccess(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER], ["propertyCode"]);
      if (!validation.success) {
        return validation.response;
      }

      const { startDate = "2025-06-08", endDate = "2025-06-15", propertyCode = "22514" } = validation.requestBody;

      query = `
        with 
        dates1 as (select cast('${startDate}' as date) startdate,'${endDate}' as enddate ,'${propertyCode}' property_code ),
        dates as(
        select cast((select enddate from dates1) as date)-30 t_30
        ,cast((select enddate from dates1) as date)-30 t_31
        , startdate
        , enddate  enddate
        ,add_months(cast((select enddate from dates1) as date),-12) as prevyrenddate
        , property_code 
        ,date_Sub(trunc(CAST((select enddate from dates1) AS DATE), 'YYYY') ,31-
        (select distinct cast(icloseday as int) from gold_dev.edlh.fact_financial_perf_ytd where trim(property_Code)=(select property_code from dates1)) ) as adjusted_period_start
        ,case when day(enddate) >= (select distinct cast(icloseday as int) from gold_dev.edlh.fact_financial_perf_ytd where trim(property_Code)=(select property_code from dates1)) 
        then MAKE_DATE(YEAR(enddate), MONTH(enddate), (select distinct cast(icloseday as int) from gold_dev.edlh.fact_financial_perf_ytd where trim(property_Code)=(select property_code from dates1)))  
        else MAKE_DATE(YEAR(enddate), MONTH(enddate)-1,
        case when day(last_day(add_months(CAST((select enddate from dates1) AS DATE), -1)))<  (select distinct cast(icloseday as int) from gold_dev.edlh.fact_financial_perf_ytd where trim(property_Code)=(select property_code from dates1)) 
        then day(last_day(add_months(CAST((select enddate from dates1) AS DATE), -1)))
        else (select distinct cast(icloseday as int) from gold_dev.edlh.fact_financial_perf_ytd where trim(property_Code)=(select property_code from dates1))  end
        )  end as adjusted_period_end
        , cast((select enddate from dates1) as date)-60 t_60
        ,trunc(CAST((select enddate from dates1) AS DATE), 'YYYY') 1st_day_of_year
        ,last_day(add_months(CAST((select enddate from dates1) AS DATE), -1)) AS last_day_prev_month
        ,trunc(CAST((select enddate from dates1) AS DATE), 'MM') AS first_day_of_month  
        from dates1
        ),
        cte as (select property_code,unit_code,sstatus
        ,row_number()over(partition by unit_code order by unit_status_hmy desc)rnk
         from gold_dev.edlh.fact_property_unitscorecard 
         where trim(property_code) in (select property_code from dates) and trim(unit_status) !='Excluded'
        and dtstart<=(select enddate from dates)  )

        ----Income 
        select 'Income' parameter,try_divide((sum(smtd1*-1) -sum(sbudget))*100,sum(sbudget)) Income from gold_dev.edlh.fact_financial_perf_ytd 
        where trim(property_code)in(select property_code from dates)
         and cast(umonth as date)>=(select 1st_day_of_year from dates)
        and cast(umonth as date)<=(select last_day_prev_month from dates) and ibook=1 and trim(Total_Income)='Total Income'
        union all
        select 'Rental Income' parameter,try_divide((sum(smtd1*-1) -sum(sbudget))*100,sum(sbudget)) Rental_Income
        from gold_dev.edlh.fact_financial_perf_ytd
        where trim(property_code)in(select property_code from dates)
        and cast(umonth as date)>=(select 1st_day_of_year from dates)
        and cast(umonth as date)<=(select last_day_prev_month from dates) and ibook=1 and (acct_code like '401%'
        or acct_code like '402%' or acct_code like '403%'  or acct_code like '404%' or acct_code like '405%'
        or acct_code like '40653%' or acct_code like '40654%' or acct_code like '40655%')
        union all 
        ----Cont OPex 
        select 'Controllable Opex'parameter,try_divide((sum(sbudget)-sum(smtd1*-1))*100,sum(sbudget)) Income
          from gold_dev.edlh.fact_financial_perf_ytd where trim(property_code)in(select property_code from dates)
         and cast(umonth as date)>=(select 1st_day_of_year from dates)
        and cast(umonth as date)<=(select last_day_prev_month from dates) and ibook=1 and trim(finance1)='Controllabe Op Exp'
        union all 
        ----total  OPex 
        select 'total OPex' parameter,try_divide((sum(sbudget)-sum(smtd1*-1))*100,sum(sbudget)) Income
          from gold_dev.edlh.fact_financial_perf_ytd where trim(property_code)in(select property_code from dates)
         and cast(umonth as date)>=(select 1st_day_of_year from dates)
        and cast(umonth as date)<=(select last_day_prev_month from dates) and ibook=1 and trim(total_opex)='Total Opex'
        union all 
        ----NOI 
        select 'NOI' parameter,try_divide((sum(smtd1*-1)-sum(sbudget))*100,sum(sbudget)) Income
          from gold_dev.edlh.fact_financial_perf_ytd where trim(property_code)in(select property_code from dates)
         and cast(umonth as date)>=(select 1st_day_of_year from dates)
        and cast(umonth as date)<=(select last_day_prev_month from dates) and ibook=1 and trim(NOI)='NOI'
        union all 
        ----Controll NOI 
        select 'Controll NOI' parameter,try_divide((sum(smtd1*-1)-sum(sbudget))*100,sum(sbudget)) Income
          from gold_dev.edlh.fact_financial_perf_ytd where trim(property_code)in(select property_code from dates)
         and cast(umonth as date)>=(select 1st_day_of_year from dates)
        and cast(umonth as date)<=(select last_day_prev_month from dates) and ibook=1 and trim(Controllable_NOI)='Controllable NOI'
        union all 
        ------Capital 
        select 'Capital' parameter,try_divide((sum(sbudget)-sum(smtd1*-1))*100,sum(sbudget)) Income
          from gold_dev.edlh.fact_financial_perf_ytd where trim(property_code)in(select property_code from dates)
         and cast(umonth as date)>=(select 1st_day_of_year from dates)
        and cast(umonth as date)<=(select last_day_prev_month from dates) and ibook=1 and trim(finance1)='Capital'
        union all 
        --- cost per turn 
        select 'COST Per Turn' parameter,try_divide(sum(smtd1)
        ,(select count(distinct tenant_code) from gold_dev.edlh.fact_lease_history
        where dtmoveout>=(select adjusted_period_start from dates) 
        and dtmoveout<=(select adjusted_period_end from dates) and trim(property_code)in(select property_code from dates)
        and tenant_code not in (select distinct tenant_code from gold_dev.edlh.fact_tenanat_details
        where trim(property_code)in(select property_code from dates) and sevent ='Promote Move Out')
        ))
         From gold_dev.edlh.fact_financial_perf_ytd 
        where cost='Cost Per Turn' and 
        trim(property_code)in(select property_code from dates)
         and cast(umonth as date)>=(select adjusted_period_start from dates)
        and cast(umonth as date)<=(select adjusted_period_end from dates) and ibook=1
      `;

      const result = await executeDatabricksQuery(query, context);

      const transformedData = {};
      result.forEach((item) => {
        const parameterName = item.parameter?.trim().replace(/\s+/g, "_");
        const value = item.Income !== undefined ? item.Income : item.value1;
        if (parameterName && value !== undefined && value !== null) {
          transformedData[parameterName] = value;
        }
        // Explicitly include 0 values
        if (parameterName && value === 0) {
          transformedData[parameterName] = 0;
        }
      });

      return {
        jsonBody: {
          sql: query,
          data: transformedData,
        },
      };
    } catch (error) {
      context.log(`Error in getScoreCardFinancial: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});

app.http("getScoreCardOperations", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "score-card-operations",
  handler: async (request, context) => {
    let query = "";
    try {
      // Validate property access
      const validation = await validatePropertyAccess(request, context, [ROLES.ADMIN, ROLES.DEVELOPER, ROLES.READER], ["propertyCode"]);
      if (!validation.success) {
        return validation.response;
      }

      const { startDate = "2025-06-08", endDate = "2025-06-15", propertyCode = "22514" } = validation.requestBody;

      query = ` 
      with 
              dates1 as (select cast('${startDate}' as date) startdate,'${endDate}' as enddate ,'${propertyCode}' property_code ),
              dates as(
              select cast((select enddate from dates1) as date)-30 t_30
              ,cast((select enddate from dates1) as date)-90 t_90
              ,cast((select enddate from dates1) as date)-30 t_31
              , startdate
              , enddate  enddate
              ,add_months(cast((select enddate from dates1) as date),-12) as prevyrenddate
              , property_code 
              ,date_Sub(trunc(CAST((select enddate from dates1) AS DATE), 'YYYY') ,31-
              (select distinct cast(icloseday as int) from gold_dev.edlh.fact_financial_perf_ytd where trim(property_Code)=(select property_code from dates1)) ) as adjusted_period_start
              ,case when day(enddate) >= (select distinct cast(icloseday as int) from gold_dev.edlh.fact_financial_perf_ytd where trim(property_Code)=(select property_code from dates1)) 
              then MAKE_DATE(YEAR(enddate), MONTH(enddate), (select distinct cast(icloseday as int) from gold_dev.edlh.fact_financial_perf_ytd where trim(property_Code)=(select property_code from dates1)))  
              else MAKE_DATE(YEAR(enddate), MONTH(enddate)-1,
              case when day(last_day(add_months(CAST((select enddate from dates1) AS DATE), -1)))<  (select distinct cast(icloseday as int) from gold_dev.edlh.fact_financial_perf_ytd where trim(property_Code)=(select property_code from dates1)) 
              then day(last_day(add_months(CAST((select enddate from dates1) AS DATE), -1)))
              else (select distinct cast(icloseday as int) from gold_dev.edlh.fact_financial_perf_ytd where trim(property_Code)=(select property_code from dates1))  end
              )  end as adjusted_period_end
              , cast((select enddate from dates1) as date)-60 t_60
              ,trunc(CAST((select enddate from dates1) AS DATE), 'YYYY') 1st_day_of_year
              ,last_day(add_months(CAST((select enddate from dates1) AS DATE), -1)) AS last_day_prev_month
              ,trunc(CAST((select enddate from dates1) AS DATE), 'MM') AS first_day_of_month  
              from dates1
              ),
              cte as (select property_code,unit_code,sstatus
              ,row_number()over(partition by unit_code order by unit_status_hmy desc)rnk
               from gold_dev.edlh.fact_property_unitscorecard 
               where trim(property_code) in (select property_code from dates) and trim(unit_status) !='Excluded'
              and dtstart<=(select enddate from dates)  ) ,
              cte_cap_exec as (select month(umonth),year(umonth),sum(smtd*-1)actual,sum(sbudget*-1) budget
              ,case when sum(smtd*-1) <= sum(sbudget*-1) then sum(smtd*-1) else sum(sbudget*-1) end as actual_budget
               from gold_dev.edlh.fact_financial_perf_ytd where trim(property_code)in(select property_code from dates) and cast(umonth as date)>=(select 1st_day_of_year from dates)
              and cast(umonth as date)<=(select last_day_prev_month from dates) and ibook=1 and left(acct_code,2)=16 
              group by month(umonth),year(umonth)
              ),
              cte_repeat_tickets as (select  concat(unit_hmy,sub_category,category) cat_hmy,max(dt_Call)dt_Call
              from gold_dev.edlh.fact_property_tickets vspt
              where  dt_call between (select t_30 from dates) and (select enddate from dates) and date_cancel  is null and unit_hmy >0
              and trim(property_code) in(select property_code from dates) group by concat(unit_hmy,sub_category,category)
              )
              ,cte_outstanding as (select  *,TIMESTAMPDIFF(hour,dt_call,case when date_comp is null then (select enddate from dates) else date_comp end) date_diff1
              from gold_dev.edlh.fact_property_tickets vspt
              where  dt_call between (select t_30 from dates) and (select enddate from dates) and date_cancel  is null 
              and trim(property_code) in(select property_code from dates)
              )
              ---------
              --collection recovery ratio
              select 'Collect recovery ratio',
              try_divide(((select sum(smtd1*-1) bad_Date_rec
               from gold_dev.edlh.fact_financial_perf_ytd where trim(property_code)in(select property_code from dates)
               and cast(umonth as date)>=(select adjusted_period_start from dates)
              and cast(umonth as date)<=(select adjusted_period_end from dates) and trim(acct_code) in ('404251000','404252000') and ibook=1 )*-100),
              (select sum(smtd1*-1) bad_date_loss
               from gold_dev.edlh.fact_financial_perf_ytd where trim(property_code)in(select property_code from dates)
               and cast(umonth as date)>=(select adjusted_period_start from dates)
              and cast(umonth as date)<=(select adjusted_period_end from dates) and TRIM(acct_code) in ('404200000','404200050') and ibook=1 )) as value1
              union all 
              select 'outstanding tickets',try_divide(sum(case when date_diff1>72 then 1 else 0 end )*100 ,
              sum(case when date_diff1 is not null then 1 else 0 end)) value1
               from cte_outstanding
               union all
               select 'total tickets < 5%',sum(case when date_diff1 is not null then 1 else 0 end)*0.05 value1
               from cte_outstanding
              union all 
              ---collection MTD 
              select 'collection MTD' Parameter,try_divide(((
              select sum(receipt_amount) 
              from gold_dev.edlh.fact_property_transactions where receipt_date<=(select enddate from dates) 
              and trim(property_code)in(select property_code from dates)
              and charge_date=(select first_day_of_month  from dates))*100),(select sum(charge_amount) from (select distinct charge_key,charge_amount 
              from gold_dev.edlh.fact_property_transactions where charge_date=(select first_day_of_month  from dates) 
              and trim(property_code)in(select property_code from dates))
              )) mtd
              union all 
              -----capital execution
               select 'capital execution' Parameter, try_divide((sum(actual)*100),sum(budget)) cap_exe from cte_cap_exec
               union all 
               ---- avf turn time 
               select 
              'avg_turn_time'parameter,avg(DATEDIFF(day,dtmoveout,dtready)) avg_turn_time
              From gold_dev.edlh.fact_property_unitscorecard where trim(property_code) in(select property_code from dates) and trim(unit_status) !='Excluded'
              and dtready >=(select t_90 from dates) and dtready<=(select enddate from dates) and dtmoveout is not null 
              union all 
              -----repeat tickets 
              select 'repeat_tickets'parameter,count(*) repeat_tickets from(
              select a.unit_hmy ,concat(sub_category,category),count(*)
              from gold_dev.edlh.fact_property_tickets a
              join cte_repeat_tickets cte on concat(a.unit_hmy,a.sub_category,a.category) =cte.cat_hmy
              and a.dt_call between cast(cte.dt_call as date)-30 and cte.dt_Call
              where
              a.dt_call between (select t_60 from dates) and (select enddate from dates) and date_cancel  is null
              and trim(property_code) in(select property_code from dates)
              group by a.unit_hmy ,concat(sub_category,category) having count(*)>1
              )
              union all 
              select 'Residential sqft',sum(unit_sqft) residential_sqft from(select distinct unit_code,unit_sqft
               from gold_dev.edlh.fact_property_unitscorecard where trim(property_code) in (select property_code from dates)
               and trim(property_code) not like '%r' and trim(unit_status) !='Excluded')
               union  all
               select 'AVG Residential sqft',avg(unit_sqft) avg_residential_sqft from(select distinct unit_code,unit_sqft
               from gold_dev.edlh.fact_property_unitscorecard 
               where trim(property_code) in (select property_code from dates) and trim(unit_status) !='Excluded' )
               union all 
               select 'Retail sqft',sum(unit_sqft)Retail_sqft from(select distinct unit_code,unit_sqft
               from gold_dev.edlh.fact_property_unitscorecard 
               where trim(property_code) in (select concat(trim(property_code),'r') from dates) and trim(unit_status) !='Excluded')
               union all 
               select 'Retail Spaces',count(distinct unit_code)Retail_spaces from(select distinct unit_code,unit_sqft
               from gold_dev.edlh.fact_property_unitscorecard 
               where trim(property_code) in (select concat(trim(property_code),'r') from dates) and trim(unit_status) !='Excluded' )
               union all 
               select 'Affordable' ,count(distinct unit_code) affordable
               from gold_dev.edlh.fact_property_unitscorecard where trim(property_code) in (select property_code from dates)
               and trim(unit_status) !='Excluded' and sfield8='Y'
               union all 
               select 'Non Revenue' ,count(distinct unit_code) non_rev
               from gold_dev.edlh.fact_property_unitscorecard where trim(property_code) in (select property_code from dates)
               and trim(unit_status) in ('Admin','Model','Down') and 
               (dtend is null or dtend >=(select enddate from dates))
                union all 
               select 'Down' ,count(distinct unit_code) down_units
               from gold_dev.edlh.fact_property_unitscorecard where trim(property_code) in (select property_code from dates) and trim(unit_status) in ('Down') and 
               (dtend is null or dtend >=(select enddate from dates))  
               union all
               select 'bad debt w/o % GRI',try_divide(((select sum(smtd1*-1) from gold_dev.edlh.fact_financial_perf_ytd where ibook=1 
               and trim(acct_code) in ('404200000','404200050')
               and umonth>=(select adjusted_period_start from dates) 
               and umonth<=(select adjusted_period_end from dates)
               AND TRIM(property_code) in (select property_code from dates))*100),(select sum(smtd1*-1) from gold_dev.edlh.fact_financial_perf_ytd 
               where ibook=1 and GRI='Y'and umonth>=(select adjusted_period_start from dates)  
               and umonth<=(select adjusted_period_end from dates) AND TRIM(property_code) in (select property_code from dates)))  bad_debt_wo
               Union all
               Select 'Adjusted_Period_Start_Month', Month(adjusted_period_start) aMonth from dates
               where property_code=(select property_code from dates)
               Union all
               Select 'Adjusted_Period_Start_Day', Day(adjusted_period_start) aMonth from dates
               where property_code=(select property_code from dates)
               Union all
                Select 'Adjusted_Period_Start_Year', Year(adjusted_period_start) aMonth from dates
               where property_code=(select property_code from dates)
               union all
               Select 'Adjusted_Period_End_Month', Month(adjusted_period_end) amonth from dates
               where property_code=(select property_code from dates)
               union all
               Select 'Adjusted_Period_End_Day', Day(adjusted_period_end) amonth from dates
               where property_code=(select property_code from dates)
               union all
               Select 'Adjusted_Period_End_Year', Year(adjusted_period_end) amonth from dates
               where property_code=(select property_code from dates)
            `;

      const result = await executeDatabricksQuery(query, context);

      const transformedData = {};
      result.forEach((item) => {
        let parameterName;

        if (item["Collect recovery ratio"] !== undefined) {
          parameterName = item["Collect recovery ratio"]?.trim().replace(/\s+/g, "_");
        } else if (item.parameter !== undefined) {
          parameterName = item.parameter?.trim().replace(/\s+/g, "_");
        }

        const value =
          item.value1 !== undefined
            ? item.value1
            : item.mtd !== undefined
            ? item.mtd
            : item.cap_exe !== undefined
            ? item.cap_exe
            : item.avg_turn_time !== undefined
            ? item.avg_turn_time
            : item.repeat_tickets !== undefined
            ? item.repeat_tickets
            : item.residential_sqft !== undefined
            ? item.residential_sqft
            : item.avg_residential_sqft !== undefined
            ? item.avg_residential_sqft
            : item.Retail_sqft !== undefined
            ? item.Retail_sqft
            : item.Retail_spaces !== undefined
            ? item.Retail_spaces
            : item.affordable !== undefined
            ? item.affordable
            : item.non_rev !== undefined
            ? item.non_rev
            : item.down_units !== undefined
            ? item.down_units
            : item.bad_debt_wo !== undefined
            ? item.bad_debt_wo
            : item.aMonth !== undefined
            ? item.aMonth
            : item.amonth !== undefined
            ? item.amonth
            : null;

        if (parameterName) {
          transformedData[parameterName] = value;
        }
      });

      return {
        jsonBody: {
          sql: query,
          data: transformedData,
        },
      };
    } catch (error) {
      context.log(`Error in getScoreCardOperations: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});

app.http("getLeaseLineItems", {
  methods: ["POST"],
  authLevel: "anonymous",
  route: "lease-line-items",
  handler: async (request, context) => {
    let query = "";
    try {
      const { startDate, endDate, propertyCode, rnkFilter } = await request.json();

      if (!startDate || !endDate || !propertyCode || !rnkFilter) {
        return {
          status: 400,
          jsonBody: { error: "Missing required parameters: startDate, endDate, propertyCode, rnkFilter" },
        };
      }

      const rnkCondition = rnkFilter === "1" ? "rnk=1" : rnkFilter === ">1" ? "rnk>1" : "rnk>=1";

      query = `
        with 
        dates1 as (select cast('${startDate}' as date) startdate,'${endDate}' as enddate ,'${propertyCode}' property_code ),
        dates as(
        select cast((select enddate from dates1) as date)-30 t_30
        ,cast((select enddate from dates1) as date)-30 t_31
        , startdate
        , enddate  enddate
        ,add_months(cast((select enddate from dates1) as date),-12) as prevyrenddate
        , property_code 
        ,date_Sub(trunc(CAST((select enddate from dates1) AS DATE), 'YYYY') ,31-
        (select distinct cast(icloseday as int) from gold_dev.edlh.fact_financial_perf_ytd where trim(property_Code)=(select property_code from dates1)) ) as adjusted_period_start
        ,case when day(enddate) >= (select distinct cast(icloseday as int) from gold_dev.edlh.fact_financial_perf_ytd where trim(property_Code)=(select property_code from dates1)) 
          then MAKE_DATE(YEAR(enddate), MONTH(enddate), (select distinct cast(icloseday as int) from gold_dev.edlh.fact_financial_perf_ytd where trim(property_Code)=(select property_code from dates1)))  
          else MAKE_DATE(YEAR(enddate), MONTH(enddate)-1,
          case when day(last_day(add_months(CAST((select enddate from dates1) AS DATE), -1)))<  (select distinct cast(icloseday as int) from gold_dev.edlh.fact_financial_perf_ytd where trim(property_Code)=(select property_code from dates1)) 
          then day(last_day(add_months(CAST((select enddate from dates1) AS DATE), -1)))
          else (select distinct cast(icloseday as int) from gold_dev.edlh.fact_financial_perf_ytd where trim(property_Code)=(select property_code from dates1))  end
        )  end as adjusted_period_end
        , cast((select enddate from dates1) as date)-60 t_60
        ,trunc(CAST((select enddate from dates1) AS DATE), 'YYYY') 1st_day_of_year
        ,last_day(add_months(CAST((select enddate from dates1) AS DATE), -1)) AS last_day_prev_month
        ,trunc(CAST((select enddate from dates1) AS DATE), 'MM') AS first_day_of_month  
        from dates1
        )   
        select property_code,
        property_name, unit_code, dtLeaseFrom, dtLeaseTo, rnk lease_no, dsqft, effective_rent, one_time_concession, recurring_concession, total_concession, amortized_concession
         from gold_dev.edlh.fact_lease_history 
        where enddate>=(select enddate from dates) and dtleasefrom<=(select enddate from dates)
        and trim(property_code)in(select property_code from dates) and ${rnkCondition}
      `;

      const result = await executeDatabricksQuery(query, context);

      return {
        jsonBody: {
          sql: query,
          data: result,
        },
      };
    } catch (error) {
      context.log(`Error in getLeaseLineItems: ${error.message}`);
      return {
        status: 500,
        jsonBody: {
          error: "Something went wrong!",
          message: error.message,
          sql: query,
        },
      };
    }
  },
});
